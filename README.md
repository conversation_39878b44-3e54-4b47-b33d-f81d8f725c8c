# Hospital Assistant System

A modern AI-powered hospital management system with natural language SQL queries, built with FastAPI backend and React frontend. This application integrates Vanna.ai with PostgreSQL databases, Ollama for LLM capabilities, and ChromaDB for vector storage, providing an intelligent interface for hospital data analysis.

## 🚀 Features

### **AI-Powered Chat Interface**
- **Natural Language to SQL:** Convert plain English questions into SQL queries using Vanna.ai
- **Real-time Streaming:** Live responses with thinking indicators and progressive content loading
- **Interactive Charts:** Automatic chart generation using Plotly for data visualization
- **Chat Session Management:** Persistent chat history with session switching and management

### **Modern Architecture**
- **FastAPI Backend:** High-performance async Python API with automatic OpenAPI documentation
- **React Frontend:** Modern React 18 application with hooks and functional components
- **Real-time Communication:** Server-sent events for streaming AI responses
- **JWT Authentication:** Secure token-based authentication with refresh tokens

### **Database Integration**
- **Dual Database Setup:** Separate databases for hospital data queries and application data
- **PostgreSQL Support:** Full PostgreSQL integration with connection pooling
- **Vector Storage:** ChromaDB for efficient similarity search and context retrieval

### **User Experience**
- **Dark/Light Theme:** Automatic theme switching with system preference detection
- **Responsive Design:** Mobile-first design optimized for all screen sizes
- **Multi-language Support:** Framework ready for internationalization
- **Dashboard Analytics:** Comprehensive statistics and data visualization

## 📁 Project Structure

```
AI-Chatbot-for-Hospital/
├── backend/                           # FastAPI Backend Application
│   ├── app/
│   │   ├── api/                      # API route modules
│   │   │   ├── analysis_routes.py    # Data analysis and chart generation
│   │   │   ├── chat_routes.py        # Chat session management
│   │   │   ├── dashboard_routes.py   # Dashboard statistics
│   │   │   ├── main_routes.py        # Health checks and version info
│   │   │   ├── search_routes.py      # Patient/doctor/consultation search
│   │   │   └── sql_routes.py         # SQL generation and execution
│   │   ├── auth/                     # Authentication system
│   │   │   ├── routes.py             # Login, logout, token management
│   │   │   └── security.py           # JWT token handling
│   │   ├── core/                     # Core business logic
│   │   │   ├── app_database.py       # Application database service
│   │   │   ├── database.py           # Vanna setup and configuration
│   │   │   └── vanna_custom.py       # Custom Vanna implementation
│   │   ├── middleware/               # Custom middleware
│   │   │   ├── security.py           # JWT authentication middleware
│   │   │   └── versioning.py         # API versioning middleware
│   │   ├── models/                   # Data models and schemas
│   │   ├── services/                 # Business logic services
│   │   └── utils/                    # Utility functions and dependencies
│   ├── config.py                     # Configuration management
│   ├── main.py                       # FastAPI application entry point
│   └── requirements.txt              # Python dependencies
├── frontend/                         # React Frontend Application
│   ├── src/
│   │   ├── components/              # React components
│   │   │   ├── Auth/                # Authentication components
│   │   │   ├── Chat/                # Chat interface components
│   │   │   ├── Dashboard/           # Dashboard widgets and charts
│   │   │   ├── Layout/              # Layout components (Header, Sidebar)
│   │   │   └── UI/                  # Reusable UI components
│   │   ├── contexts/                # React contexts (Auth, Chat, Theme)
│   │   ├── pages/                   # Main page components
│   │   ├── utils/                   # Frontend utilities
│   │   └── styles/                  # CSS and styling files
│   ├── package.json                 # Node.js dependencies
│   └── tailwind.config.js           # Tailwind CSS configuration
├── utils/                           # Shared utilities
│   └── database_manager.py          # Database management tools
├── docker-compose.yml               # Container orchestration
├── pyproject.toml                   # Project configuration and dependencies
└── README.md                        # Project documentation
```

## 🔧 How it Works

This application provides an intelligent interface for querying hospital databases using natural language, powered by modern web technologies and AI.

### **Architecture Overview**

The system uses a **FastAPI + React** architecture with real-time communication:

- **React Frontend** (`http://localhost:3000`) - Modern single-page application
- **FastAPI Backend** (`http://localhost:8000`) - High-performance async API
- **Real-time Streaming** - Server-sent events for live AI responses
- **Dual Database Setup** - Separate PostgreSQL instances for hospital data and app data

![Architecture Diagram](images/architecture-diagram.png)

*System architecture showing the complete flow from user interaction through AI processing to database queries and real-time responses.*

### **Core Workflow**

**1. Application Startup**
- **FastAPI Backend** initializes via `main.py` with lifespan management
- **Vanna Setup**: Custom `MyVanna` class combines Ollama LLM + ChromaDB vector store
- **Database Connections**: Connects to both hospital and application PostgreSQL databases
- **Training Data**: Loads existing ChromaDB embeddings or performs initial training
- **React Frontend** serves from development server with proxy to backend API

**2. User Authentication & Interface**
- **JWT Authentication**: Secure token-based auth with refresh tokens
- **React Router**: Client-side routing with protected routes
- **Theme System**: Dark/light mode with system preference detection
- **Responsive Design**: Mobile-first layout with Tailwind CSS

**3. Natural Language Query Processing**

When a user asks a question:

**Frontend (`/pages/Chatbot.jsx`)**:
- User types question → `ChatInput` component
- Creates `EventSource` connection to `/api/v1/sql/generate_sql`
- Streams real-time responses with thinking indicators

**Backend (`/api/sql_routes.py`)**:
- `generate_sql_api()` endpoint processes the natural language question
- **Vanna Context Retrieval**: Searches ChromaDB for relevant:
  - Similar question-SQL pairs
  - Table schemas (DDL statements)
  - Documentation snippets
- **LLM Processing**: Sends enriched prompt to Ollama model
- **Streaming Response**: Returns SQL + explanations via Server-Sent Events

**4. Intelligent SQL Processing**

**Intermediate SQL Execution**:
When the LLM determines that data exploration is needed to answer a question accurately, it can generate intermediate SQL queries:

- **Multi-step Analysis**: The system can execute multiple intermediate queries to understand data structure and content
- **Data Exploration**: Each intermediate query explores specific aspects of the data (e.g., checking available values, understanding relationships)
- **Context Building**: Results from intermediate queries are added to the model's context for generating the final, accurate SQL
- **Iterative Refinement**: Up to 5 iterations of intermediate queries can be executed to build comprehensive understanding
- **Streaming Feedback**: Users see each intermediate step with explanations like "Executing intermediate query #1 to explore data..."

**Auto SQL Correction**:
When SQL execution fails, the system automatically attempts to fix the query:

- **Error Analysis**: The LLM analyzes the original question, failed SQL, and database error message
- **Context Enhancement**: Retrieves relevant table schemas, documentation, and similar successful queries
- **Intelligent Correction**: Generates a corrected SQL query that addresses the specific error while maintaining the original intent
- **Validation**: Ensures the corrected SQL is syntactically valid and different from the original
- **Fallback Handling**: If correction fails, provides detailed error information to the user

**SQL Execution** (`run_sql_api()`):
- Validates and executes generated SQL on hospital database
- Applies automatic `LIMIT` for safety (configurable via `MAX_SQL_ROWS`)
- **Error Handling**: Automatic correction attempts with detailed error reporting
- Returns results as pandas DataFrame with size limits for performance

**Data Visualization**:
- **Summaries** (`/api/analysis/generate_summary_stream`): LLM creates data insights
- **Charts** (`/api/analysis/generate_plotly_figure`): Auto-generates Plotly visualizations
- **Tables**: Responsive data tables with scroll and pagination

**5. Advanced Features**

**Chat Session Management** (`/api/chat/`):
- Persistent chat sessions with SQLite storage
- Session switching and history management
- Message threading and context preservation

**Dashboard Analytics** (`/api/dashboard/`):
- Real-time hospital statistics
- Patient demographics and trends
- Interactive charts and metrics

**Search Capabilities** (`/api/search/`):
- Advanced patient/doctor/consultation search
- Vector-based semantic search using embeddings
- Filtered and paginated results

### **Key Technologies**

- **Backend**: FastAPI, SQLAlchemy, Pandas, Plotly, ChromaDB, Ollama
- **Frontend**: React 18, React Router, Tailwind CSS, Plotly.js
- **Database**: PostgreSQL (dual setup), ChromaDB (vector storage)
- **AI/ML**: Vanna.ai, Ollama LLM, Vector embeddings
- **Authentication**: JWT tokens, bcrypt password hashing
- **Real-time**: Server-Sent Events (EventSource)

## ⚙️ Configuration

The application uses environment variables for configuration. Create a `.env` file in the `backend/` directory:

```env
# Flask/FastAPI Configuration
SECRET_KEY=your_secret_key_here
DEBUG=True

# Hospital Database Configuration (for SQL queries)
HOSPITAL_DB_HOST=your-hospital-db-host.com
HOSPITAL_DB_NAME=postgres
HOSPITAL_DB_USER=postgres.your_user
HOSPITAL_DB_PASSWORD=your_hospital_db_password
HOSPITAL_DB_PORT=5432

# App Database Configuration (for authentication and app data)
APP_DB_HOST=your-app-db-host.com
APP_DB_NAME=postgres
APP_DB_USER=postgres.your_app_user
APP_DB_PASSWORD=your_app_db_password
APP_DB_PORT=6543

# Legacy Database Configuration (backward compatibility)
DB_HOST=your-hospital-db-host.com
DB_NAME=postgres
DB_USER=postgres.your_user
DB_PASSWORD=your_hospital_db_password
DB_PORT=5432

# Ollama Configuration
OLLAMA_MODEL=sam860/qwen3:4b-Q4_K_XL
OLLAMA_HOST=127.0.0.1
OLLAMA_PORT=11434

# Cache Configuration (Optional - defaults provided)
CACHE_MAX_SIZE=150
CACHE_EXPIRATION_SECONDS=7200
CACHE_DIR=persistent_cache

# SQL Execution Limits (Optional - defaults provided)
MAX_SQL_ROWS=1000
MAX_DF_SIZE_MB=50
SAMPLE_DF_SIZE_MB=10

# Advanced SQL Features (Optional - defaults provided)
ENABLE_INTERMEDIATE_SQL=True
MAX_INTERMEDIATE_ITERATIONS=5
ENABLE_AUTO_CORRECTION=True
```

### Important Notes

- Both `HOSPITAL_DB_PASSWORD` and `APP_DB_PASSWORD` are mandatory
- The application uses **dual database setup**:
  - **Hospital DB**: Contains medical/patient data for SQL queries
  - **App DB**: Contains user accounts, chat sessions, and application data
- All database ports must be valid (1-65535)
- Chat history persists in the `CACHE_DIR` directory

### Advanced SQL Features Configuration

- **Intermediate SQL**: Set `ENABLE_INTERMEDIATE_SQL=True` to allow multi-step data exploration
- **Auto Correction**: Set `ENABLE_AUTO_CORRECTION=True` to automatically fix failed SQL queries
- **Iteration Limits**: `MAX_INTERMEDIATE_ITERATIONS` controls how many intermediate queries can be executed (default: 5)
- These features require `allow_llm_to_see_data=True` to function properly

## 🚀 Quick Start

### Prerequisites

- **Python 3.11+** for backend
- **Node.js 16+** for frontend
- **PostgreSQL** databases (hospital data + app data)
- **Ollama** running locally with your preferred model

### Installation & Setup

1. **Clone the Repository**
   ```bash
   git clone https://github.com/your-username/AI-Chatbot-for-Hospital.git
   cd AI-Chatbot-for-Hospital
   ```

2. **Backend Setup**
   ```bash
   cd backend
   
   # Create virtual environment
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   
   # Install dependencies
   pip install -r requirements.txt
   
   # Create .env file with your configuration
   cp .env.example .env  # Edit with your settings
   ```

3. **Frontend Setup**
   ```bash
   cd frontend
   
   # Install dependencies
   npm install
   ```

4. **Database Setup**
   ```bash
   # Set up your PostgreSQL databases
   # Create hospital database with your medical data
   # Create app database for user management
   
   # Run database migrations (if needed)
   cd backend
   python manage_db.py
   ```

5. **Start the Application**

   **Backend (Terminal 1):**
   ```bash
   cd backend
   python main.py
   # or
   uvicorn main:app --host 0.0.0.0 --port 8000 --reload
   ```

   **Frontend (Terminal 2):**
   ```bash
   cd frontend
   npm start
   ```

6. **Access the Application**
   - **Frontend**: `http://localhost:3000`
   - **Backend API**: `http://localhost:8000`
   - **API Documentation**: `http://localhost:8000/docs`

### Default Demo Accounts

- **Admin**: `admin` / `admin123`
- **Doctor**: `doctor` / `doctor123`
- **Nurse**: `nurse` / `nurse123`

## 📊 API Documentation

The FastAPI backend provides comprehensive API documentation:

- **Interactive Docs**: `http://localhost:8000/docs` (Swagger UI)
- **ReDoc**: `http://localhost:8000/redoc` (Alternative documentation)
- **OpenAPI Schema**: `http://localhost:8000/openapi.json`

### API Endpoints Overview

**Authentication** (`/api/v1/auth/`):
- `POST /login` - User authentication
- `POST /logout` - User logout
- `GET /me` - Current user info
- `POST /refresh` - Token refresh

**SQL Generation** (`/api/v1/sql/`):
- `GET /generate_sql` - Natural language to SQL with intermediate SQL support (streaming)
- `GET /run_sql` - Execute generated SQL with automatic error correction

**Data Analysis** (`/api/v1/analysis/`):
- `GET /generate_summary_stream` - AI-generated data summaries
- `GET /generate_plotly_figure` - Interactive chart generation

**Dashboard** (`/api/v1/dashboard/`):
- `GET /overview` - System statistics
- `GET /patients` - Patient demographics
- `GET /consultations` - Consultation metrics

**Chat Sessions** (`/api/v1/chat/`):
- `POST /sessions` - Create chat session
- `GET /sessions` - List user sessions
- `GET /sessions/{id}` - Get session details
- `POST /sessions/{id}/messages` - Add message to session

## 🔒 Security Features

- **JWT Authentication**: Secure token-based authentication with automatic refresh
- **Password Hashing**: Bcrypt encryption for user passwords
- **SQL Injection Protection**: Parameterized queries and input validation
- **CORS Configuration**: Configurable cross-origin resource sharing
- **Rate Limiting**: Built-in FastAPI rate limiting capabilities
- **Environment Variables**: Sensitive data stored in environment variables

## 📝 Logging & Monitoring

The application provides comprehensive logging:

- **Application Logs**: Stored in `backend/app.log`
- **Console Output**: Real-time logs in terminal
- **Request Logging**: Automatic API request/response logging
- **Error Tracking**: Detailed error messages and stack traces
- **Performance Monitoring**: Request timing and processing metrics

## 🧩 Advanced Features

### Intelligent SQL Processing

#### **Intermediate SQL Execution**

The system supports sophisticated multi-step SQL generation for complex queries that require data exploration:

**How It Works**:
1. **Detection**: When the LLM determines that understanding the data structure or content is necessary, it generates an intermediate SQL query
2. **Execution**: The intermediate query is executed to explore the database (e.g., checking available values, understanding relationships)
3. **Context Enhancement**: Results are added to the model's context as markdown tables
4. **Iteration**: The process can repeat up to 5 times for comprehensive data understanding
5. **Final Generation**: With full context, the LLM generates the accurate final SQL query

**Key Features**:
- **Multi-step Analysis**: Handles complex questions requiring data exploration
- **Real-time Feedback**: Users see each intermediate step with explanations
- **Context Preservation**: All intermediate results are maintained in chat history
- **Safety Limits**: Configurable limits prevent infinite loops and excessive resource usage
- **Streaming Interface**: Progressive display of intermediate queries and results

**Example Workflow**:
```
User: "Show me all patients with diabetes"

Step 1: Intermediate SQL - "SELECT DISTINCT diagnosis FROM patients
                           WHERE diagnosis ILIKE '%diabetes%' LIMIT 20"
        → Explores exact diabetes-related diagnosis strings in database
        → Results: "Type 2 Diabetes Mellitus", "Diabetes Type 1", "Gestational Diabetes"

Step 2: Final SQL - "SELECT patient_id, patient_name, diagnosis, diagnosis_date
                     FROM patients
                     WHERE diagnosis IN ('Type 2 Diabetes Mellitus', 'Diabetes Type 1', 'Gestational Diabetes')
                     ORDER BY diagnosis_date DESC"
```

#### **Automatic SQL Correction**

When SQL execution fails, the system intelligently attempts to fix the query:

**Correction Process**:
1. **Error Analysis**: Captures the original question, failed SQL, and database error message
2. **Context Retrieval**: Fetches relevant table schemas, documentation, and successful query examples
3. **Intelligent Repair**: LLM analyzes the error and generates a corrected SQL query
4. **Validation**: Ensures the correction is syntactically valid and addresses the specific error
5. **Execution**: Attempts to run the corrected SQL automatically

**Correction Capabilities**:
- **Syntax Errors**: Fixes SQL syntax issues (missing commas, incorrect keywords)
- **Schema Mismatches**: Corrects table/column name errors using available DDL
- **Type Mismatches**: Resolves data type conflicts and casting issues
- **Logic Errors**: Improves query logic based on similar successful examples
- **Performance Issues**: Adds appropriate indexes or query optimizations

**Error Handling Flow**:
```
Original SQL Fails → Auto-Correction Attempt → Success/Failure Response
                                            ↓
                  If Correction Fails → Detailed Error Report with Context
```

### Chat Session Management

- **Persistent Sessions**: SQLite storage for chat history
- **Session Switching**: Seamless switching between conversations
- **Context Preservation**: Maintains conversation context across sessions
- **Search & Organization**: Find and organize previous conversations

### Dashboard Analytics

- **Real-time Statistics**: Live hospital metrics and KPIs
- **Interactive Charts**: Plotly.js visualizations with theme support
- **Data Export**: CSV download capabilities
- **Responsive Design**: Mobile-optimized dashboard layout

## 🛠️ Development

### Project Technologies

**Backend Stack**:
- **FastAPI**: Modern Python web framework
- **SQLAlchemy**: Database ORM
- **Pandas**: Data manipulation
- **ChromaDB**: Vector database
- **Ollama**: Local LLM integration
- **Plotly**: Chart generation

**Frontend Stack**:
- **React 18**: Modern component-based UI
- **React Router**: Client-side routing
- **Tailwind CSS**: Utility-first styling
- **Context API**: State management
- **Plotly.js**: Interactive charts

### Development Commands

**Backend Development**:
```bash
cd backend

# Run with auto-reload
uvicorn main:app --reload

# Run tests
pytest

# Database migrations
python manage_db.py
```

**Frontend Development**:
```bash
cd frontend

# Development server
npm start

# Build for production
npm run build

# Run tests
npm test
```
